"""Security utilities for safe file operations and data handling.

This module provides security-focused utilities for the Yemen Market Integration
project, ensuring safe file operations, input validation, and secure data handling.

Classes:
    SecureFileHandler: Safe file operations with validation
    InputValidator: Input validation and sanitization
    DataSanitizer: Data cleaning and sanitization utilities

Example:
    >>> from yemen_market.utils.security import SecureFileHandler
    >>> handler = SecureFileHandler()
    >>> data = handler.safe_load_csv("data.csv")
"""

import os
import hashlib
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
import warnings
import logging
from contextlib import contextmanager

from yemen_market.utils.logging import info, warning, error, bind

# Set module context
bind(module=__name__)


class SecurityError(Exception):
    """Custom exception for security-related errors."""
    pass


class SecureFileHandler:
    """Secure file operations with validation and safety checks."""
    
    # Allowed file extensions for different operations
    ALLOWED_DATA_EXTENSIONS = {'.csv', '.parquet', '.xlsx', '.json', '.pkl', '.pickle'}
    ALLOWED_CONFIG_EXTENSIONS = {'.json', '.yaml', '.yml', '.toml', '.ini'}
    ALLOWED_OUTPUT_EXTENSIONS = {'.csv', '.parquet', '.json', '.pkl', '.pickle', '.html', '.pdf'}
    
    # Maximum file sizes (in bytes)
    MAX_FILE_SIZE = 1024 * 1024 * 1024  # 1GB
    MAX_CONFIG_SIZE = 10 * 1024 * 1024  # 10MB
    
    def __init__(self, base_path: Optional[Union[str, Path]] = None):
        """Initialize secure file handler.
        
        Parameters
        ----------
        base_path : str or Path, optional
            Base directory for file operations. If None, uses current directory.
        """
        self.base_path = Path(base_path) if base_path else Path.cwd()
        self.base_path = self.base_path.resolve()
        
        info(f"Initialized SecureFileHandler with base path: {self.base_path}")
    
    def validate_path(self, file_path: Union[str, Path], 
                     operation: str = "read") -> Path:
        """Validate file path for security issues.
        
        Parameters
        ----------
        file_path : str or Path
            Path to validate
        operation : str
            Type of operation ('read', 'write', 'config')
            
        Returns
        -------
        Path
            Validated and resolved path
            
        Raises
        ------
        SecurityError
            If path is unsafe or invalid
        """
        path = Path(file_path).resolve()
        
        # Check for path traversal attacks
        try:
            path.relative_to(self.base_path)
        except ValueError:
            # Allow absolute paths in specific safe directories
            safe_dirs = [
                Path.home() / "Documents",
                Path("/tmp"),
                Path("/var/tmp"),
                Path.cwd()
            ]
            
            if not any(str(path).startswith(str(safe_dir)) for safe_dir in safe_dirs):
                raise SecurityError(f"Path outside allowed directories: {path}")
        
        # Check file extension
        if operation == "read" and path.suffix.lower() not in self.ALLOWED_DATA_EXTENSIONS:
            raise SecurityError(f"File extension not allowed for reading: {path.suffix}")
        elif operation == "config" and path.suffix.lower() not in self.ALLOWED_CONFIG_EXTENSIONS:
            raise SecurityError(f"File extension not allowed for config: {path.suffix}")
        elif operation == "write" and path.suffix.lower() not in self.ALLOWED_OUTPUT_EXTENSIONS:
            raise SecurityError(f"File extension not allowed for writing: {path.suffix}")
        
        # Check file size for existing files
        if path.exists():
            file_size = path.stat().st_size
            max_size = self.MAX_CONFIG_SIZE if operation == "config" else self.MAX_FILE_SIZE
            
            if file_size > max_size:
                raise SecurityError(f"File too large: {file_size} bytes (max: {max_size})")
        
        return path
    
    def safe_load_csv(self, file_path: Union[str, Path], **kwargs) -> pd.DataFrame:
        """Safely load CSV file with validation.
        
        Parameters
        ----------
        file_path : str or Path
            Path to CSV file
        **kwargs
            Additional arguments for pd.read_csv
            
        Returns
        -------
        pd.DataFrame
            Loaded data
        """
        path = self.validate_path(file_path, "read")
        
        try:
            # Use safe defaults
            safe_kwargs = {
                'encoding': 'utf-8',
                'engine': 'python',  # More secure than C engine
                'low_memory': False,
                **kwargs
            }
            
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", FutureWarning)
                data = pd.read_csv(path, **safe_kwargs)
            
            info(f"Safely loaded CSV: {path} ({data.shape[0]} rows, {data.shape[1]} columns)")
            return data
            
        except Exception as e:
            error(f"Failed to load CSV {path}: {e}")
            raise
    
    def safe_save_csv(self, data: pd.DataFrame, file_path: Union[str, Path], 
                     **kwargs) -> None:
        """Safely save DataFrame to CSV.
        
        Parameters
        ----------
        data : pd.DataFrame
            Data to save
        file_path : str or Path
            Output path
        **kwargs
            Additional arguments for to_csv
        """
        path = self.validate_path(file_path, "write")
        
        # Create directory if needed
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # Use safe defaults
        safe_kwargs = {
            'encoding': 'utf-8',
            'index': False,
            **kwargs
        }
        
        try:
            data.to_csv(path, **safe_kwargs)
            info(f"Safely saved CSV: {path}")
        except Exception as e:
            error(f"Failed to save CSV {path}: {e}")
            raise
    
    @contextmanager
    def secure_temp_file(self, suffix: str = ".tmp"):
        """Create a secure temporary file.
        
        Parameters
        ----------
        suffix : str
            File suffix
            
        Yields
        ------
        Path
            Path to temporary file
        """
        with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as tmp:
            temp_path = Path(tmp.name)
        
        try:
            yield temp_path
        finally:
            if temp_path.exists():
                temp_path.unlink()
    
    def compute_file_hash(self, file_path: Union[str, Path]) -> str:
        """Compute SHA-256 hash of file.
        
        Parameters
        ----------
        file_path : str or Path
            Path to file
            
        Returns
        -------
        str
            SHA-256 hash
        """
        path = self.validate_path(file_path, "read")
        
        sha256_hash = hashlib.sha256()
        with open(path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()


class InputValidator:
    """Input validation and sanitization utilities."""
    
    @staticmethod
    def validate_dataframe(data: pd.DataFrame, 
                          required_columns: Optional[List[str]] = None,
                          min_rows: int = 1) -> Tuple[bool, List[str]]:
        """Validate DataFrame structure and content.
        
        Parameters
        ----------
        data : pd.DataFrame
            Data to validate
        required_columns : list, optional
            Required column names
        min_rows : int
            Minimum number of rows
            
        Returns
        -------
        tuple
            (is_valid, list_of_errors)
        """
        errors = []
        
        # Check if DataFrame is empty
        if data.empty:
            errors.append("DataFrame is empty")
            return False, errors
        
        # Check minimum rows
        if len(data) < min_rows:
            errors.append(f"DataFrame has {len(data)} rows, minimum required: {min_rows}")
        
        # Check required columns
        if required_columns:
            missing_cols = set(required_columns) - set(data.columns)
            if missing_cols:
                errors.append(f"Missing required columns: {missing_cols}")
        
        # Check for suspicious column names
        suspicious_patterns = ['__', 'eval', 'exec', 'import', 'os.', 'sys.']
        for col in data.columns:
            if any(pattern in str(col).lower() for pattern in suspicious_patterns):
                errors.append(f"Suspicious column name: {col}")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 1000) -> str:
        """Sanitize string input.
        
        Parameters
        ----------
        value : str
            String to sanitize
        max_length : int
            Maximum allowed length
            
        Returns
        -------
        str
            Sanitized string
        """
        if not isinstance(value, str):
            value = str(value)
        
        # Truncate if too long
        if len(value) > max_length:
            value = value[:max_length]
            warning(f"String truncated to {max_length} characters")
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '&', '"', "'", '`', '\x00']
        for char in dangerous_chars:
            value = value.replace(char, '')
        
        return value.strip()


class DataSanitizer:
    """Data cleaning and sanitization utilities."""
    
    @staticmethod
    def sanitize_dataframe(data: pd.DataFrame) -> pd.DataFrame:
        """Sanitize DataFrame content.
        
        Parameters
        ----------
        data : pd.DataFrame
            Data to sanitize
            
        Returns
        -------
        pd.DataFrame
            Sanitized data
        """
        data = data.copy()
        
        # Sanitize string columns
        for col in data.select_dtypes(include=['object']).columns:
            data[col] = data[col].apply(
                lambda x: InputValidator.sanitize_string(str(x)) if pd.notna(x) else x
            )
        
        # Handle infinite values
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        data[numeric_cols] = data[numeric_cols].replace([np.inf, -np.inf], np.nan)
        
        # Log sanitization results
        info(f"Sanitized DataFrame: {data.shape[0]} rows, {data.shape[1]} columns")
        
        return data
    
    @staticmethod
    def detect_outliers(data: pd.Series, method: str = "iqr", 
                       threshold: float = 1.5) -> pd.Series:
        """Detect outliers in numeric data.
        
        Parameters
        ----------
        data : pd.Series
            Numeric data
        method : str
            Detection method ('iqr', 'zscore')
        threshold : float
            Threshold for outlier detection
            
        Returns
        -------
        pd.Series
            Boolean series indicating outliers
        """
        if method == "iqr":
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            return (data < lower_bound) | (data > upper_bound)
        
        elif method == "zscore":
            z_scores = np.abs((data - data.mean()) / data.std())
            return z_scores > threshold
        
        else:
            raise ValueError(f"Unknown outlier detection method: {method}")
