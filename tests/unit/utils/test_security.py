"""Tests for security utilities module.

This module contains comprehensive tests for the security utilities,
ensuring safe file operations and data handling capabilities.
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import warnings

from yemen_market.utils.security import (
    SecurityError, SecureFileHandler, InputValidator, DataSanitizer
)


class TestSecurityError:
    """Test suite for SecurityError exception."""
    
    def test_security_error_creation(self):
        """Test SecurityError exception creation."""
        error = SecurityError("Test security error")
        assert str(error) == "Test security error"
        assert isinstance(error, Exception)
    
    def test_security_error_inheritance(self):
        """Test SecurityError inheritance."""
        error = SecurityError("Test error")
        assert isinstance(error, Exception)


class TestSecureFileHandler:
    """Test suite for SecureFileHandler class."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield Path(tmpdir)
    
    @pytest.fixture
    def secure_handler(self, temp_dir):
        """Create a SecureFileHandler instance for testing."""
        return SecureFileHandler(base_path=temp_dir)
    
    @pytest.fixture
    def secure_handler_default(self):
        """Create a SecureFileHandler with default base path."""
        return SecureFileHandler()
    
    def test_initialization_custom_path(self, temp_dir, secure_handler):
        """Test initialization with custom base path."""
        assert secure_handler.base_path == temp_dir
    
    def test_initialization_default_path(self, secure_handler_default):
        """Test initialization with default base path."""
        assert secure_handler_default.base_path == Path.cwd()
    
    def test_validate_path_safe(self, secure_handler, temp_dir):
        """Test path validation with safe path."""
        safe_file = temp_dir / "safe_file.csv"
        
        validated_path = secure_handler.validate_path(safe_file, "read")
        assert validated_path == safe_file.resolve()
    
    def test_validate_path_traversal_attack(self, secure_handler, temp_dir):
        """Test path validation against path traversal attacks."""
        malicious_path = temp_dir / ".." / ".." / "etc" / "passwd"
        
        with pytest.raises(SecurityError, match="Path outside allowed directories"):
            secure_handler.validate_path(malicious_path, "read")
    
    def test_validate_path_nonexistent_read(self, secure_handler, temp_dir):
        """Test path validation for reading nonexistent file."""
        nonexistent_file = temp_dir / "nonexistent.csv"
        
        with pytest.raises(SecurityError, match="File does not exist"):
            secure_handler.validate_path(nonexistent_file, "read")
    
    def test_validate_path_write_operation(self, secure_handler, temp_dir):
        """Test path validation for write operation."""
        new_file = temp_dir / "new_file.csv"
        
        validated_path = secure_handler.validate_path(new_file, "write")
        assert validated_path == new_file.resolve()
    
    def test_validate_path_invalid_extension(self, secure_handler, temp_dir):
        """Test path validation with invalid file extension."""
        invalid_file = temp_dir / "malicious.exe"
        
        with pytest.raises(SecurityError, match="File extension not allowed"):
            secure_handler.validate_path(invalid_file, "read")
    
    def test_validate_file_size_normal(self, secure_handler, temp_dir):
        """Test file size validation with normal file."""
        test_file = temp_dir / "test.csv"
        test_file.write_text("small,test,file\n1,2,3")
        
        # Should not raise exception
        secure_handler.validate_file_size(test_file)
    
    def test_validate_file_size_too_large(self, secure_handler, temp_dir):
        """Test file size validation with oversized file."""
        test_file = temp_dir / "large.csv"
        
        with patch.object(test_file, 'stat') as mock_stat:
            mock_stat.return_value.st_size = SecureFileHandler.MAX_FILE_SIZE + 1
            
            with pytest.raises(SecurityError, match="File too large"):
                secure_handler.validate_file_size(test_file)
    
    def test_safe_load_csv_basic(self, secure_handler, temp_dir):
        """Test safe CSV loading with valid file."""
        test_file = temp_dir / "test.csv"
        test_data = "col1,col2,col3\n1,2,3\n4,5,6"
        test_file.write_text(test_data)
        
        df = secure_handler.safe_load_csv(test_file)
        
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 2
        assert list(df.columns) == ['col1', 'col2', 'col3']
    
    def test_safe_load_csv_with_kwargs(self, secure_handler, temp_dir):
        """Test safe CSV loading with additional kwargs."""
        test_file = temp_dir / "test.csv"
        test_data = "col1;col2;col3\n1;2;3\n4;5;6"
        test_file.write_text(test_data)
        
        df = secure_handler.safe_load_csv(test_file, sep=';')
        
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 2
    
    def test_safe_load_csv_malformed(self, secure_handler, temp_dir):
        """Test safe CSV loading with malformed file."""
        test_file = temp_dir / "malformed.csv"
        test_data = "col1,col2\n1,2,3,4\n5,6"  # Inconsistent columns
        test_file.write_text(test_data)
        
        # Should handle malformed CSV gracefully
        df = secure_handler.safe_load_csv(test_file)
        assert isinstance(df, pd.DataFrame)
    
    def test_safe_save_csv_basic(self, secure_handler, temp_dir):
        """Test safe CSV saving."""
        test_file = temp_dir / "output.csv"
        df = pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]})
        
        secure_handler.safe_save_csv(df, test_file)
        
        assert test_file.exists()
        
        # Verify content
        loaded_df = pd.read_csv(test_file)
        pd.testing.assert_frame_equal(df, loaded_df)
    
    def test_safe_save_csv_with_kwargs(self, secure_handler, temp_dir):
        """Test safe CSV saving with additional kwargs."""
        test_file = temp_dir / "output.csv"
        df = pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]})
        
        secure_handler.safe_save_csv(df, test_file, index=False, sep=';')
        
        assert test_file.exists()
    
    def test_safe_load_json_basic(self, secure_handler, temp_dir):
        """Test safe JSON loading."""
        test_file = temp_dir / "test.json"
        test_data = {"key1": "value1", "key2": [1, 2, 3]}
        
        with open(test_file, 'w') as f:
            json.dump(test_data, f)
        
        loaded_data = secure_handler.safe_load_json(test_file)
        
        assert loaded_data == test_data
    
    def test_safe_load_json_invalid(self, secure_handler, temp_dir):
        """Test safe JSON loading with invalid JSON."""
        test_file = temp_dir / "invalid.json"
        test_file.write_text("invalid json content")
        
        with pytest.raises(Exception):  # Should raise JSON decode error
            secure_handler.safe_load_json(test_file)
    
    def test_safe_save_json_basic(self, secure_handler, temp_dir):
        """Test safe JSON saving."""
        test_file = temp_dir / "output.json"
        test_data = {"key1": "value1", "key2": [1, 2, 3]}
        
        secure_handler.safe_save_json(test_data, test_file)
        
        assert test_file.exists()
        
        # Verify content
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        
        assert loaded_data == test_data
    
    def test_safe_save_json_with_kwargs(self, secure_handler, temp_dir):
        """Test safe JSON saving with additional kwargs."""
        test_file = temp_dir / "output.json"
        test_data = {"key1": "value1", "key2": [1, 2, 3]}
        
        secure_handler.safe_save_json(test_data, test_file, indent=2)
        
        assert test_file.exists()


class TestInputValidator:
    """Test suite for InputValidator class."""
    
    @pytest.fixture
    def validator(self):
        """Create an InputValidator instance for testing."""
        return InputValidator()
    
    def test_validate_string_basic(self, validator):
        """Test basic string validation."""
        valid_string = "Hello World"
        result = validator.validate_string(valid_string, max_length=20)
        assert result == valid_string
    
    def test_validate_string_too_long(self, validator):
        """Test string validation with excessive length."""
        long_string = "a" * 1000
        
        with pytest.raises(ValueError, match="String too long"):
            validator.validate_string(long_string, max_length=100)
    
    def test_validate_string_with_pattern(self, validator):
        """Test string validation with regex pattern."""
        email = "<EMAIL>"
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        result = validator.validate_string(email, pattern=email_pattern)
        assert result == email
    
    def test_validate_string_invalid_pattern(self, validator):
        """Test string validation with invalid pattern."""
        invalid_email = "invalid-email"
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        with pytest.raises(ValueError, match="String does not match required pattern"):
            validator.validate_string(invalid_email, pattern=email_pattern)
    
    def test_validate_numeric_integer(self, validator):
        """Test numeric validation with integer."""
        result = validator.validate_numeric(42, min_value=0, max_value=100)
        assert result == 42
    
    def test_validate_numeric_float(self, validator):
        """Test numeric validation with float."""
        result = validator.validate_numeric(3.14, min_value=0, max_value=10)
        assert result == 3.14
    
    def test_validate_numeric_out_of_range(self, validator):
        """Test numeric validation with out-of-range value."""
        with pytest.raises(ValueError, match="Value out of allowed range"):
            validator.validate_numeric(150, min_value=0, max_value=100)
    
    def test_validate_numeric_non_numeric(self, validator):
        """Test numeric validation with non-numeric input."""
        with pytest.raises(ValueError, match="Value must be numeric"):
            validator.validate_numeric("not a number")
    
    def test_validate_date_string_valid(self, validator):
        """Test date validation with valid date string."""
        date_str = "2023-12-25"
        result = validator.validate_date_string(date_str)
        assert result == date_str
    
    def test_validate_date_string_invalid_format(self, validator):
        """Test date validation with invalid format."""
        invalid_date = "25/12/2023"  # Wrong format
        
        with pytest.raises(ValueError, match="Invalid date format"):
            validator.validate_date_string(invalid_date)
    
    def test_validate_date_string_invalid_date(self, validator):
        """Test date validation with invalid date."""
        invalid_date = "2023-02-30"  # February 30th doesn't exist
        
        with pytest.raises(ValueError, match="Invalid date"):
            validator.validate_date_string(invalid_date)
    
    def test_validate_date_string_custom_format(self, validator):
        """Test date validation with custom format."""
        date_str = "25/12/2023"
        result = validator.validate_date_string(date_str, date_format="%d/%m/%Y")
        assert result == date_str
    
    def test_sanitize_input_basic(self, validator):
        """Test basic input sanitization."""
        dirty_input = "  Hello World!  "
        result = validator.sanitize_input(dirty_input)
        assert result == "Hello World!"
    
    def test_sanitize_input_html_tags(self, validator):
        """Test input sanitization with HTML tags."""
        html_input = "<script>alert('xss')</script>Hello"
        result = validator.sanitize_input(html_input)
        # Should remove HTML tags
        assert "<script>" not in result
        assert "Hello" in result
    
    def test_sanitize_input_special_chars(self, validator):
        """Test input sanitization with special characters."""
        special_input = "Hello & <World> \"Test\""
        result = validator.sanitize_input(special_input)
        # Should escape or remove special characters
        assert isinstance(result, str)


class TestDataSanitizer:
    """Test suite for DataSanitizer class."""
    
    def test_clean_dataframe_basic(self):
        """Test basic DataFrame cleaning."""
        df = pd.DataFrame({
            'A': [1, 2, np.nan, 4],
            'B': ['a', 'b', 'c', 'd'],
            'C': [1.1, 2.2, 3.3, np.inf]
        })
        
        cleaned_df = DataSanitizer.clean_dataframe(df)
        
        assert isinstance(cleaned_df, pd.DataFrame)
        assert len(cleaned_df) <= len(df)  # May remove rows with issues
    
    def test_clean_dataframe_remove_duplicates(self):
        """Test DataFrame cleaning with duplicate removal."""
        df = pd.DataFrame({
            'A': [1, 2, 2, 3],
            'B': ['a', 'b', 'b', 'c']
        })
        
        cleaned_df = DataSanitizer.clean_dataframe(df, remove_duplicates=True)
        
        assert len(cleaned_df) == 3  # One duplicate removed
    
    def test_clean_dataframe_handle_missing(self):
        """Test DataFrame cleaning with missing value handling."""
        df = pd.DataFrame({
            'A': [1, 2, np.nan, 4],
            'B': [1.1, np.nan, 3.3, 4.4]
        })
        
        # Test different strategies
        cleaned_drop = DataSanitizer.clean_dataframe(df, handle_missing='drop')
        cleaned_fill = DataSanitizer.clean_dataframe(df, handle_missing='fill')
        
        assert len(cleaned_drop) < len(df)
        assert len(cleaned_fill) == len(df)
        assert not cleaned_fill.isnull().any().any()
    
    def test_remove_outliers_iqr(self):
        """Test outlier removal using IQR method."""
        # Create data with outliers
        normal_data = np.random.normal(0, 1, 100)
        outliers = np.array([10, -10, 15])
        data_with_outliers = np.concatenate([normal_data, outliers])
        
        series = pd.Series(data_with_outliers)
        
        cleaned_series = DataSanitizer.remove_outliers(series, method='iqr')
        
        assert len(cleaned_series) < len(series)
        assert cleaned_series.max() < 10  # Extreme outliers removed
    
    def test_remove_outliers_zscore(self):
        """Test outlier removal using z-score method."""
        # Create data with outliers
        normal_data = np.random.normal(0, 1, 100)
        outliers = np.array([5, -5])
        data_with_outliers = np.concatenate([normal_data, outliers])
        
        series = pd.Series(data_with_outliers)
        
        cleaned_series = DataSanitizer.remove_outliers(series, method='zscore', threshold=3)
        
        assert len(cleaned_series) <= len(series)
    
    def test_remove_outliers_invalid_method(self):
        """Test outlier removal with invalid method."""
        series = pd.Series([1, 2, 3, 4, 5])
        
        with pytest.raises(ValueError, match="Unknown outlier removal method"):
            DataSanitizer.remove_outliers(series, method='invalid')
    
    def test_detect_outliers_iqr(self):
        """Test outlier detection using IQR method."""
        data = pd.Series([1, 2, 3, 4, 5, 100])  # 100 is an outlier
        
        outlier_mask = DataSanitizer.detect_outliers(data, method='iqr')
        
        assert isinstance(outlier_mask, pd.Series)
        assert outlier_mask.dtype == bool
        assert outlier_mask.iloc[-1] == True  # Last value (100) should be outlier
    
    def test_detect_outliers_zscore(self):
        """Test outlier detection using z-score method."""
        data = pd.Series([1, 2, 3, 4, 5, 100])  # 100 is an outlier
        
        outlier_mask = DataSanitizer.detect_outliers(data, method='zscore', threshold=2)
        
        assert isinstance(outlier_mask, pd.Series)
        assert outlier_mask.dtype == bool
    
    def test_detect_outliers_invalid_method(self):
        """Test outlier detection with invalid method."""
        data = pd.Series([1, 2, 3, 4, 5])
        
        with pytest.raises(ValueError, match="Unknown outlier detection method"):
            DataSanitizer.detect_outliers(data, method='invalid')


class TestIntegration:
    """Integration tests for security utilities."""
    
    def test_secure_workflow(self, tmp_path):
        """Test a complete secure data processing workflow."""
        # Setup secure handler
        handler = SecureFileHandler(base_path=tmp_path)
        validator = InputValidator()
        
        # Create test data
        df = pd.DataFrame({
            'name': ['Alice', 'Bob', 'Charlie'],
            'age': [25, 30, 35],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        })
        
        # Save securely
        output_file = tmp_path / "secure_data.csv"
        handler.safe_save_csv(df, output_file)
        
        # Load securely
        loaded_df = handler.safe_load_csv(output_file)
        
        # Validate and clean
        cleaned_df = DataSanitizer.clean_dataframe(loaded_df)
        
        # Validate specific fields
        for email in cleaned_df['email']:
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            validated_email = validator.validate_string(email, pattern=email_pattern)
            assert validated_email == email
        
        assert len(cleaned_df) == 3
        assert list(cleaned_df.columns) == ['name', 'age', 'email']
